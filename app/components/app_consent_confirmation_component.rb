# frozen_string_literal: true

class AppConsentConfirmationComponent < ViewComponent::Base
  erb_template <<-ERB
    <%= govuk_panel(title_text: title, text: panel_text) %>

    <p>We've sent a confirmation to <%= parent_email %></p>
  ERB

  def initialize(consent_form)
    super

    @consent_form = consent_form
  end

  def title
    if response_given?
      if refused_programmes.empty?
        "<PERSON><PERSON> confirmed"
      else
        "Consent for the #{given_vaccinations} confirmed"
      end
    else
      "Con<PERSON> refused"
    end
  end

  private

  delegate :given_programmes,
           :refused_programmes,
           :response_given?,
           :parent_email,
           to: :@consent_form

  def full_name
    @consent_form.full_name(context: :parents)
  end

  def panel_text
    if response_given?
      if @consent_form.needs_triage?
        <<-END_OF_TEXT
          As you answered ‘yes’ to some of the health questions, we need to check
          the #{given_vaccinations_are} suitable for #{full_name}. We’ll review
          your answers and get in touch again soon.
        END_OF_TEXT
      else
        "#{full_name} is due to get the #{given_vaccinations} at school" +
          (session_dates.present? ? " on #{session_dates}" : "")
      end
    else
      "You’ve told us that you do not want #{full_name} to get the" \
        " #{refused_vaccinations} at school"
    end
  end

  def given_vaccinations = vaccinations_text(given_programmes)

  def refused_vaccinations = vaccinations_text(refused_programmes)

  def vaccinations_text(programmes)
    programme_names =
      programmes.map do |programme|
        programme.type == "flu" ? "nasal flu" : programme.name
      end

    "#{programme_names.to_sentence} vaccination".pluralize(
      programme_names.count
    )
  end

  def given_vaccinations_are
    "#{given_vaccinations} #{given_programmes.one? ? "is" : "are"}"
  end

  def session_dates
    @consent_form
      .actual_session
      .today_or_future_dates
      .map { it.to_fs(:short_day_of_week) }
      .to_sentence(two_words_connector: " or ", last_word_connector: " or ")
  end
end
