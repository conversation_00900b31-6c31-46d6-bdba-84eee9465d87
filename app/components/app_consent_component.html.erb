<%= render AppConsentStatusComponent.new(patient_session:, programme:) %>

<% if consent_status.no_response? %>
  <% if latest_consent_request %>
    <p class="nhsuk-body">
      No-one responded to our requests for consent.
    </p>

    <p class="nhsuk-body">
      A request was sent on <%= latest_consent_request.sent_at.to_fs(:long) %>.
    </p>
  <% else %>
    <p class="nhsuk-body">
      No requests have been sent.
    </p>
  <% end %>
<% end %>

<% if consents.present? %>
  <%= govuk_table(classes: "nhsuk-u-margin-bottom-4") do |table| %>
    <%= table.with_head do |head| %>
      <%= head.with_row do |row| %>
        <%= row.with_cell(text: "Name") %>
        <%= row.with_cell(text: "Response date") %>
        <%= row.with_cell(text: "Decision") %>
      <% end %>
    <% end %>

    <%= table.with_body do |body| %>
      <% consents.each do |consent| %>
        <%= body.with_row do |row| %>
          <%= row.with_cell(classes: "app-table__cell-status--#{status_colour(consent)}") do %>
            <%= govuk_link_to consent&.parent&.full_name || consent.patient.full_name,
                              session_patient_programme_consent_path(session, patient, programme, consent) %>
            <br>
            <span class="nhsuk-u-font-size-16"><%= consent.who_responded %></span>
          <% end %>
          <%= row.with_cell(
                classes: "app-table__cell-status--#{status_colour(consent)}",
                text: consent.responded_at.to_fs(:long),
              ) %>
          <%= row.with_cell(
                classes: "app-table__cell-status--#{status_colour(consent)}",
                text: helpers.consent_decision(consent),
              ) %>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
<% end %>

<% if can_send_consent_request? %>
  <%= govuk_button_to "Send consent request",
                      send_request_session_patient_programme_consents_path(
                        session, patient, programme
                      ),
                      secondary: true %>
<% end %>

<%= govuk_button_to "Get consent response",
                    session_patient_programme_consents_path(
                      session, patient, programme
                    ),
                    secondary: true %>
