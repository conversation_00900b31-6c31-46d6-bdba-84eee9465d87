<%= form_with(
     model: vaccinate_form,
     url:,
     method: :post,
     class: "nhsuk-card",
     builder: GOVUKDesignSystemFormBuilder::FormBuilder,
   ) do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <div class="nhsuk-card__content">
    <section>
      <h2 class="nhsuk-card__heading nhsuk-heading-m">
        Pre-screening checks
      </h2>

      <p><%= patient.given_name %> has confirmed that they:</p>

      <ul class="nhsuk-list nhsuk-list--bullet">
        <li>are not acutely unwell</li>
        <% if vaccinate_form.ask_not_pregnant? %>
          <li>are not pregnant</li>
        <% end %>
        <% if vaccinate_form.ask_not_taking_medication? %>
          <li>are not taking any medication which prevents vaccination</li>
        <% end %>
        <li>have no allergies which would prevent vaccination</li>
        <li>have not already had this vaccination</li>
        <li>know what the vaccination is for, and are happy to have it</li>
      </ul>

      <%= f.govuk_check_boxes_fieldset :pre_screening_confirmed, multiple: false, legend: nil do %>
        <%= f.govuk_check_box :pre_screening_confirmed, 1, 0, multiple: false, link_errors: true,
                                                              label: { text: "#{patient.given_name} has confirmed the above statements are true" } %>
      <% end %>

      <%= f.govuk_text_area :pre_screening_notes, label: { text: "Pre-screening notes (optional)" }, rows: 3 %>
    </section>

    <hr class="nhsuk-section-break nhsuk-section-break--visible nhsuk-section-break--l">

    <section>
      <h2 class="nhsuk-card__heading nhsuk-heading-m">
        Is <%= patient.given_name %> ready for their <%= programme.name %> vaccination?
      </h2>

      <% hint = "Pre-screening checks must be completed for vaccination to go ahead" %>

      <%= f.govuk_radio_buttons_fieldset :administered, legend: nil do %>
        <% if common_delivery_sites_options.length > 1 %>
          <%= f.govuk_radio_button :administered, true, label: { text: "Yes" }, hint: { text: hint }, link_errors: true do %>
            <%= f.govuk_collection_radio_buttons :delivery_site,
                                                 common_delivery_sites_options,
                                                 :value,
                                                 :label,
                                                 legend: {
                                                   text: "Where will the injection be given?",
                                                   size: "s",
                                                 } %>
          <% end %>
        <% else %>
          <%= f.govuk_radio_button :administered, true, label: { text: "Yes" }, hint: { text: hint }, link_errors: true %>
          <%= f.hidden_field :delivery_site, value: common_delivery_sites_options.first.value %>
        <% end %>
        <%= f.govuk_radio_button :administered, false, label: { text: "No" } %>
      <% end %>

      <%= f.hidden_field :delivery_method, value: delivery_method %>
      <%= f.hidden_field :dose_sequence, value: dose_sequence %>
      <%= f.hidden_field :programme_id, value: programme.id %>

      <%= f.govuk_submit "Continue" %>
    </section>
  </div>
<% end %>
