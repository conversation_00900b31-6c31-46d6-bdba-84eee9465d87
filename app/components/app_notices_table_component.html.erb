<div class="nhsuk-table__panel-with-heading-tab">
  <h3 class="nhsuk-table__heading-tab">
    <%= pluralize(notices.count, "notice") %>
  </h3>

  <%= govuk_table(html_attributes: {
                    class: "nhsuk-table-responsive",
                  }) do |table| %>
    <% table.with_head do |head| %>
      <% head.with_row do |row| %>
        <% row.with_cell(text: "Date") %>
        <% row.with_cell(text: "Child") %>
        <% row.with_cell(text: "Notice") %>
      <% end %>
    <% end %>

    <% table.with_body do |body| %>
      <% notices.each do |notice| %>
        <% body.with_row do |row| %>
          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Date</span>
            <%= notice[:date_time].to_date.to_fs(:long) %>
          <% end %>
          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Child</span>
            <%= link_to notice[:patient].full_name, patient_path(notice[:patient]) %>
          <% end %>
          <% row.with_cell do %>
            <span class="nhsuk-table-responsive__heading">Notice</span>
            <%= notice[:message] %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
</div>
