// Ensure the autocomplete uses the correct typeface
.autocomplete__wrapper {
  font-family: $nhsuk-font;
}

.autocomplete__input {
  font-family: inherit;
}

// Style the autocomplete if there’s an error
.nhsuk-form-group--error {
  .autocomplete__input {
    border-color: $nhsuk-error-color;
  }

  .autocomplete__input--focused {
    border-color: $nhsuk-form-border-color;
  }
}

.autocomplete__dropdown-arrow-down {
  // Ensure dropdown arrow can be clicked
  // https://github.com/alphagov/accessible-autocomplete/issues/202
  pointer-events: none;
  // Ensure dropdown arrow can be seen
  z-index: 0;
}

.autocomplete__input {
  background-color: $nhsuk-form-element-background-color;
}

.autocomplete__option {
  margin-bottom: 0;
}

.autocomplete__option-hint {
  color: $nhsuk-secondary-text-color;

  .autocomplete__option:hover &,
  .autocomplete__option:focus & {
    color: $color_nhsuk-grey-5;
  }
}
