// Search filters and results
.app-grid-column-filters .nhsuk-card--feature.app-filters,
.app-grid-column-results > .nhsuk-card--feature,
.app-grid-column-results > .nhsuk-warning-callout {
  margin-top: nhsuk-spacing(3);
}

.app-grid-column-filters {
  @include nhsuk-grid-column(filters, $at: large-desktop, $class: false);
}

.app-grid-column-results {
  @include nhsuk-grid-column(results, $at: large-desktop, $class: false);
}

// Sticky column
.app-grid-column--sticky {
  @include nhsuk-media-query($from: desktop) {
    position: sticky;
    top: 0;
  }
}

.app-grid-column--sticky-below-secondary-navigation {
  @include nhsuk-media-query($from: desktop) {
    position: sticky;
    top: #{nhsuk-spacing(9) + nhsuk-spacing(3)};
  }
}
