// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application";

import AutocompleteController from "./autocomplete_controller";
application.register("autocomplete", AutocompleteController);

import AutosubmitController from "./autosubmit_controller";
application.register("autosubmit", AutosubmitController);

import CheckAllController from "./check_all_controller";
application.register("check-all", CheckAllController);

import GovukCharacterCountController from "./govuk_character_count_controller";
application.register("govuk-character-count", GovukCharacterCountController);

import GovukCheckboxesController from "./govuk_checkboxes_controller";
application.register("govuk-checkboxes", GovukCheckboxesController);

import NhsukButtonController from "./nhsuk_button_controller";
application.register("nhsuk-button", NhsukButtonController);

import NhsukErrorSummaryController from "./nhsuk_error_summary_controller";
application.register("nhsuk-error-summary", NhsukErrorSummaryController);

import NhsukHeaderController from "./nhsuk_header_controller";
application.register("nhsuk-header", NhsukHeaderController);

import NhsukNotificationBannerController from "./nhsuk_notification_banner_controller";
application.register(
  "nhsuk-notification-banner",
  NhsukNotificationBannerController,
);

import NhsukPasswordInputController from "./nhsuk_password_input_controller";
application.register("nhsuk-password-input", NhsukPasswordInputController);

import NhsukRadiosController from "./nhsuk_radios_controller";
application.register("nhsuk-radios", NhsukRadiosController);

import NhsukTabsController from "./nhsuk_tabs_controller";
application.register("nhsuk-tabs", NhsukTabsController);
