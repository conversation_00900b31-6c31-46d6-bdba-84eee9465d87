<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<%= form_with model: @draft_vaccination_record, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <span class="nhsuk-caption-l"><%= @patient.full_name %></span>
  <%= h1 "When was the #{@programme.name} vaccination given?" %>

  <%= f.govuk_date_field :performed_at,
                         legend: { text: "Date" },
                         hint: { text: "For example, 27 3 2017" } %>

  <% form_group_class = ["nhsuk-form-group", @draft_vaccination_record.errors[:performed_at].present? ? "nhsuk-form-group--error" : nil].compact %>
  <% date_input_class = ["nhsuk-date-input__input", @draft_vaccination_record.errors[:performed_at].present? ? "nhsuk-input--error" : nil].compact %>

  <%= tag.div(class: form_group_class) do %>
    <%= f.govuk_fieldset legend: { text: "Time" }, hint: { text: "For example, 13 15" } do %>
      <div class="nhsuk-date-input">
        <div class="nhsuk-date-input__item">
          <%= f.govuk_text_field :"performed_at(4i)", class: date_input_class, label: { text: "Hour", class: "nhsuk-date-input__label" }, value: @draft_vaccination_record.performed_at&.hour, width: 2 %>
        </div>

        <div class="nhsuk-date-input__item">
          <%= f.govuk_text_field :"performed_at(5i)", class: date_input_class, label: { text: "Minute", class: "nhsuk-date-input__label" }, value: @draft_vaccination_record.performed_at&.min&.to_s&.rjust(2, "0"), width: 2 %>
        </div>
      </div>
    <% end %>
  <% end %>

  <%= f.govuk_submit %>
<% end %>
