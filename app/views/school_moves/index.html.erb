<%= h1 "School moves", size: "xl" %>

<div class="nhsuk-u-reading-width">
  <p>When imported records or a new consent response indicates that a child has changed school, <PERSON><PERSON> flags this as a school move.</p>
  <p>You can then review the new information and confirm the school move or ignore it.</p>

  <%= govuk_button_to "Download records", school_move_exports_path, secondary: true, class: "nhsuk-u-margin-bottom-5" %>
</div>

<% if @school_moves.any? %>
  <div class="nhsuk-table__panel-with-heading-tab">
    <h3 class="nhsuk-table__heading-tab">
      <%= pluralize(@pagy.count, "school move") %>
    </h3>

    <%= govuk_table(html_attributes: { class: "nhsuk-table-responsive" }) do |table| %>
      <% table.with_head do |head| %>
        <% head.with_row do |row| %>
          <% row.with_cell(text: "Updated") %>
          <% row.with_cell(text: "Full name") %>
          <% row.with_cell(text: "Move") %>
          <% row.with_cell(text: "Actions") %>
        <% end %>
      <% end %>

      <% table.with_body do |body| %>
        <% @school_moves.each do |school_move| %>
          <% body.with_row do |row| %>
            <% row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Updated</span>
              <%= school_move.updated_at.to_fs(:long) %>
            <% end %>

            <% row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Full name</span>
              <%= school_move.patient.full_name %>
            <% end %>

            <% row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Move</span>

              <span>
                <span class="nhsuk-u-secondary-text-color nhsuk-u-font-size-16">
                  <%= school_move_source(school_move) %>
                </span>
                <br />
                <%= patient_school(school_move.patient) %>
                <br />
                <span class="nhsuk-u-secondary-text-color nhsuk-u-font-size-16">to</span>
                <%= patient_school(school_move) %>
              </span>
            <% end %>

            <% row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Actions</span>
              <%= link_to "Review", school_move_path(school_move) %>
            <% end %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <%= govuk_pagination(pagy: @pagy) %>
<% else %>
  <p class="nhsuk-body">There are currently no school moves.</p>
<% end %>
