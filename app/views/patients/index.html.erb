<%= h1 t(".title"), size: "xl" %>

<%= render AppSearchComponent.new(form: @form, url: patients_path) %>

<% if @patients.any? %>
  <%= render AppPatientTableComponent.new(@patients, current_user:, count: @pagy.count) %>
<% else %>
  <%= render AppCardComponent.new(colour: "blue") do |card| %>
    <% card.with_heading { "No children" } %>
    <% card.with_description { "No children matching search criteria found." } %>
  <% end %>
<% end %>

<%= govuk_pagination(pagy: @pagy) %>

<% if @pagy.count.positive? %>
  <p class="nhsuk-body">
    Showing <b><%= @pagy.from %></b> to <b><%= @pagy.to %></b> of <b><%= @pagy.count %></b> children
  </p>
<% end %>
