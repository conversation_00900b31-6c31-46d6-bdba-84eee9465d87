<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(patient_path(@patient), name: @patient.full_name) %>
<% end %>

<%= h1 "Edit child record" %>

<% change_links = {
     nhs_number: edit_nhs_number_patient_path(@patient),
     parent: @patient.parent_relationships.each_with_object({}) do |parent_relationship, memo|
       memo[parent_relationship.parent_id] = edit_patient_parent_relationship_path(@patient, parent_relationship.parent_id)
     end,
   } %>

<% remove_links = {
     parent: @patient.parent_relationships.each_with_object({}) do |parent_relationship, memo|
       memo[parent_relationship.parent_id] = destroy_patient_parent_relationship_path(@patient, parent_relationship.parent_id)
     end,
   } %>

<%= render AppPatientCardComponent.new(@patient, change_links:, remove_links:) %>

<%= govuk_button_link_to "Continue", patient_path(@patient) %>
