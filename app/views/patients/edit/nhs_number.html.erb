<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(edit_patient_path(@patient), name: "edit patient") %>
<% end %>

<% legend = "What is the child’s NHS number?" %>
<% content_for :page_title, legend %>

<%= form_with model: @patient, url: edit_nhs_number_patient_path(@patient), method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_text_field :nhs_number,
                         caption: { text: @patient.full_name, size: "l" },
                         label: {
                           tag: "h1",
                           text: legend,
                           size: "l",
                         } %>

  <%= f.govuk_submit "Continue" %>
<% end %>
