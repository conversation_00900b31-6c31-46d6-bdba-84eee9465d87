<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("patients.index.title"), href: patients_path },
                                        ]) %>
<% end %>

<%= h1 page_title: @patient.initials do %>
  <%= @patient.full_name %>
<% end %>

<%= render AppSecondaryNavigationComponent.new do |nav|
      nav.with_item(href: patient_path(@patient), text: "Child record", selected: true)
      nav.with_item(href: log_patient_path(@patient), text: "Activity log")
    end %>

<%= render AppPatientCardComponent.new(@patient) do %>
  <%= govuk_button_link_to "Edit child record", edit_patient_path(@patient), secondary: true %>
<% end %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "Cohorts" } %>
  <%= render AppPatientCohortTableComponent.new(@patient, current_user:) %>
<% end %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "Sessions" } %>
  <%= render AppPatientSessionTableComponent.new(@patient_sessions) %>
<% end %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "Vaccinations" } %>
  <%= render AppPatientVaccinationTableComponent.new(@patient) %>
<% end %>
