<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% page_title = "Is it safe to vaccinate?" %>

<%= h1 page_title: do %>
  <span class="nhsuk-caption-l">
    <%= @patient.full_name %>
  </span>
  <%= page_title %>
<% end %>

<%= form_with model: @draft_consent, url: wizard_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_radio_buttons_fieldset(:triage_status, legend: nil) do %>
    <%= f.govuk_radio_button(
          :triage_status, :ready_to_vaccinate,
          label: { text: "Yes, it’s safe to vaccinate" },
          link_errors: true,
        ) %>
    <%= f.govuk_radio_divider %>
    <%= f.govuk_radio_button(
          :triage_status, :do_not_vaccinate,
          label: { text: "No, do not vaccinate" },
        ) %>
    <%= f.govuk_radio_button(
          :triage_status, :delay_vaccination,
          label: { text: "No, delay vaccination (and invite to clinic)" },
        ) %>
    <%= f.govuk_radio_button(
          :triage_status, :needs_follow_up,
          label: { text: "No, keep in triage" },
        ) %>
  <% end %>

  <%= f.govuk_text_area(
        :triage_notes,
        label: { text: "Triage notes (optional)" },
        rows: 5,
      ) %>

  <%= f.govuk_submit "Continue" %>
<% end %>
