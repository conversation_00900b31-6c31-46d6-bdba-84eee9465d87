<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% title = "Why are they refusing to give consent?" %>
<% content_for :page_title, title %>

<%= form_with model: @draft_consent, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>
  <%= f.govuk_radio_buttons_fieldset(:reason_for_refusal,
                                     caption: { size: "l",
                                                text: @patient.full_name },
                                     legend: { size: "l",
                                               tag: "h1",
                                               text: title }) do %>
    <%= f.govuk_radio_button :reason_for_refusal, "already_vaccinated",
                             label: { text: "Vaccine already received" }, link_errors: true %>
    <%= f.govuk_radio_button :reason_for_refusal, "will_be_vaccinated_elsewhere",
                             label: { text: "Vaccine will be given elsewhere" } %>
    <%= f.govuk_radio_button :reason_for_refusal, "medical_reasons",
                             label: { text: "Medical reasons" } %>
    <%= f.govuk_radio_button :reason_for_refusal, "personal_choice",
                             label: { text: "Personal choice" } %>
    <%= f.govuk_radio_divider %>
    <%= f.govuk_radio_button :reason_for_refusal, "other",
                             label: { text: "Other" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
