<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(
        session_patient_programme_path(@session, @patient, @programme),
        name: @patient.full_name,
      ) %>
<% end %>

<% page_title = @gillick_assessment.persisted? ? "Edit Gillick competence" : "Assess Gillick competence" %>

<%= h1 page_title: do %>
  <span class="nhsuk-caption-l">
    <%= @patient.full_name %>
  </span>
  <%= page_title %>
<% end %>

<%= govuk_inset_text do %>
  <p class="nhsuk-body">
    Before you make your assessment, you should give
    <%= @patient.given_name %> a chance to ask questions.
  </p>
<% end %>

<%= form_with model: @gillick_assessment, url: session_patient_programme_gillick_assessment_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <% options = [OpenStruct.new(value: true, label: "Yes"), OpenStruct.new(value: false, label: "No")] %>

  <%= f.govuk_collection_radio_buttons :knows_vaccination,
                                       options,
                                       :value,
                                       :label,
                                       inline: true,
                                       legend: { text: "The child knows which vaccination they will have", size: nil } %>

  <%= f.govuk_collection_radio_buttons :knows_disease,
                                       options,
                                       :value,
                                       :label,
                                       inline: true,
                                       legend: { text: "The child knows which disease the vaccination protects against", size: nil } %>

  <%= f.govuk_collection_radio_buttons :knows_consequences,
                                       options,
                                       :value,
                                       :label,
                                       inline: true,
                                       legend: { text: "The child knows what could happen if they got the disease", size: nil } %>

  <%= f.govuk_collection_radio_buttons :knows_delivery,
                                       options,
                                       :value,
                                       :label,
                                       inline: true,
                                       legend: { text: "The child knows how the injection will be given", size: nil } %>

  <%= f.govuk_collection_radio_buttons :knows_side_effects,
                                       options,
                                       :value,
                                       :label,
                                       inline: true,
                                       legend: { text: "The child knows which side effects they might experience", size: nil } %>

  <%= f.govuk_text_area :notes, label: { text: "Assessment notes (optional)" } %>

  <%= f.govuk_submit @gillick_assessment.persisted? ? "Update your assessment" : "Complete your assessment" %>
<% end %>
