<% content_for :page_title, @patient.initials %>

<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: "Home", href: dashboard_path },
                                          { text: t("sessions.index.title"), href: sessions_path },
                                          { text: @session.location.name, href: session_path(@session) },
                                          @breadcrumb_item,
                                        ].compact) %>
<% end %>

<% if policy(VaccinationRecord).new? && (outstanding_programmes = @patient_session.outstanding_programmes).any? %>
  <%= govuk_notification_banner(title_text: "Important") do |notification_banner| %>
    <% notification_banner.with_heading(text: "You still need to record an outcome for #{outstanding_programmes.map(&:name).to_sentence}.") %>
  <% end %>
<% end %>

<h1 class="nhsuk-heading-l nhsuk-u-margin-bottom-2">
  <%= @patient.full_name %>
</h1>

<p class="nhsuk-caption-l nhsuk-u-margin-bottom-4">
  <%= patient_year_group(@patient) %>
</p>

<ul class="app-action-list">
  <% if (session_attendance = @patient_session.todays_attendance) %>
    <li class="app-action-list__item">
      <%= render AppRegisterStatusTagComponent.new(@patient_session.registration_status&.status || "unknown") %>
    </li>

    <% if policy(session_attendance).edit? %>
      <li class="app-action-list__item">
        <%= link_to(
              "Update attendance",
              edit_session_patient_session_attendance_path(@session, @patient)
            ) %>
      </li>
    <% end %>
  <% end %>

  <% if policy(VaccinationRecord).create? && @programme && @patient_session.can_record_as_already_vaccinated?(programme: @programme) %>
    <li class="app-action-list__item">
      <%= link_to "Record as already vaccinated",
                  session_patient_programme_record_already_vaccinated_path(@session, @patient, @programme) %>
    </li>
  <% end %>
</ul>

<%= render AppSecondaryNavigationComponent.new(classes: "app-secondary-navigation--sticky") do |nav|
      @patient_session.programmes.each do |programme|
        nav.with_item(
          href: session_patient_programme_path(@session, @patient, programme, return_to: params[:return_to]),
          text: programme.name,
          selected: @programme == programme,
          ticked: @patient.vaccination_status(programme:).vaccinated?,
        )
      end
    
      nav.with_item(
        href: session_patient_activity_path(@session, @patient, return_to: params[:return_to]),
        text: "Activity log",
        selected: request.path.ends_with?("/activity"),
      )
    end %>
