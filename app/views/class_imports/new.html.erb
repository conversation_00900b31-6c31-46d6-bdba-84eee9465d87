<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(draft_class_import_path("session")) %>
<% end %>

<% title = "Import class list records" %>

<% content_for :page_title, title %>

<%= form_with model: @class_import, url: class_imports_path do |f| %>
  <%= f.govuk_error_summary %>

  <h1 class="nhsuk-heading-l">
    <span class="nhsuk-caption-l"><%= @session.location.name %></span>
    <%= title %>
  </h1>

  <p>The file you upload should use the Mavis CSV format for class list records.</p>

  <%= render AppImportFormatDetailsComponent.new(import: @class_import) %>

  <%= f.govuk_file_field :csv, label: { text: "Upload file", size: "m" } %>

  <%= f.govuk_submit %>
<% end %>
