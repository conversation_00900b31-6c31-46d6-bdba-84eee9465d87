<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("imports.index.title"), href: imports_path },
                                          { text: "Import issues", href: imports_issues_path },
                                        ]) %>
<% end %>

<% if @vaccination_record %>
  <% title = "Review duplicate vaccination record" %>
<% else %>
  <% title = "Review duplicate child record" %>
<% end %>

<span class="nhsuk-caption-l"><%= @patient.full_name %></span>
<%= h1 title, page_title: "#{@patient.full_name} – #{title}" %>

<%= render AppWarningCalloutComponent.new(
      heading: "This record needs reviewing",
      description: "A field in a duplicate record does not match that in a previously uploaded record.",
    ) %>

<div class="nhsuk-grid-row">
  <div class="nhsuk-grid-column-one-half">
    <%= render AppCardComponent.new(colour: "blue") do |c| %>
      <% c.with_heading { "Duplicate record" } %>
      <h3 class="nhsuk-heading-s">Duplicate child record</h3>
      <%= render AppChildSummaryComponent.new(@patient.with_pending_changes) %>
      <% if @vaccination_record %>
        <h3 class="nhsuk-heading-s">Duplicate vaccination record</h3>
        <%= render AppVaccinationRecordSummaryComponent.new(
              @vaccination_record.with_pending_changes, current_user:,
            ) %>
      <% end %>
    <% end %>
  </div>

  <div class="nhsuk-grid-column-one-half">
    <%= render AppCardComponent.new(colour: "blue") do |c| %>
      <% c.with_heading { "Existing record" } %>
      <h3 class="nhsuk-heading-s">Existing child record</h3>
      <%= render AppChildSummaryComponent.new(@patient) %>
      <% if @vaccination_record %>
        <h3 class="nhsuk-heading-s">Existing vaccination record</h3>
        <%= render AppVaccinationRecordSummaryComponent.new(
              @vaccination_record, current_user:,
            ) %>
      <% end %>
    <% end %>
  </div>
</div>

<%= form_with(
      model: @form,
      url: imports_issue_path(@record, type: params[:type]),
      method: :patch,
      class: "nhsuk-u-width-one-half",
    ) do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_collection_radio_buttons :apply_changes, @form.apply_changes_options, :itself, nil %>

  <%= f.govuk_submit "Resolve duplicate" %>
<% end %>
