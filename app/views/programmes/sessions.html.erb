<%= content_for :page_title, "#{@programme.name} – Sessions" %>

<% content_for :before_main do %>
  <%= render AppBreadcrumbComponent.new(items: [
                                          { text: t("dashboard.index.title"), href: dashboard_path },
                                          { text: t("programmes.index.title"), href: programmes_path },
                                          { text: @programme.name, href: programme_path(@programme) },
                                        ]) %>
<% end %>

<h1 class="nhsuk-heading-l"><%= @programme.name %></h1>

<%= render AppProgrammeNavigationComponent.new(@programme, active: :sessions) %>

<%= render AppProgrammeSessionTableComponent.new(@sessions, programme: @programme) %>
