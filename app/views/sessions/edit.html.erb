<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(session_path(@session), name: @session.location.name) %>
<% end %>

<%= h1 "Edit session" do %>
  <span class="nhsuk-caption-l"><%= @session.location.name %></span>
  Edit session
<% end %>

<%= render AppCardComponent.new do |card| %>
  <% card.with_heading { "Session details" } %>

  <%= govuk_summary_list do |summary_list|
        summary_list.with_row do |row|
          row.with_key { "Programmes" }
          row.with_value { render AppProgrammeTagsComponent.new(@session.programmes) }
          row.with_action(text: "Change", href: edit_programmes_session_path(@session), visually_hidden_text: "programmes")
        end
      
        summary_list.with_row do |row|
          row.with_key { "Session dates" }
      
          if (dates = @session.dates).present?
            row.with_value do
              safe_join(dates.map { _1.to_fs(:long_day_of_week) }, tag.br)
            end
            row.with_action(text: "Change", href: session_dates_path(@session), visually_hidden_text: "session dates")
          else
            row.with_value do
              govuk_link_to "Add session dates", session_dates_path(@session)
            end
          end
        end
      
        if (send_consent_requests_at = @session.send_consent_requests_at).present?
          summary_list.with_row do |row|
            row.with_key { "Consent requests" }
            row.with_value { "Send on #{send_consent_requests_at.to_fs(:long_day_of_week)}" }
            if @session.can_change_notification_dates?
              row.with_action(text: "Change", href: edit_send_consent_requests_at_session_path(@session), visually_hidden_text: "consent requests")
            end
          end
        end
      
        if (send_consent_reminders_at = @session.send_consent_reminders_at).present?
          summary_list.with_row do |row|
            row.with_key { "Consent reminders" }
            row.with_value do
              safe_join([
                "Send #{pluralize(@session.weeks_before_consent_reminders, "week")} before each session",
                tag.span("Next: #{send_consent_reminders_at.to_fs(:long_day_of_week)}", class: "nhsuk-u-secondary-text-color"),
              ], tag.br)
            end
            if @session.can_change_notification_dates?
              row.with_action(text: "Change", href: edit_weeks_before_consent_reminders_session_path(@session), visually_hidden_text: "consent reminders")
            end
          end
        end
      
        if (send_invitations_at = @session.send_invitations_at).present?
          summary_list.with_row do |row|
            row.with_key { "Invitations" }
            row.with_value { "Send on #{send_invitations_at.to_fs(:long_day_of_week)}" }
            if @session.can_change_notification_dates?
              row.with_action(text: "Change", href: edit_send_invitations_at_session_path(@session), visually_hidden_text: "invitations")
            end
          end
        end
      end %>
<% end %>

<%= govuk_button_link_to "Continue", session_path(@session) %>
