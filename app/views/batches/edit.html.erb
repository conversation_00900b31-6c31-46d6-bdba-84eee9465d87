<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(vaccines_path, name: "manage vaccines") %>
<% end %>

<% page_title = "Edit batch #{@batch.name}" %>
<%= h1 page_title do %>
  <span class="nhsuk-caption-l">
    <%= @vaccine.brand %>
  </span>
  <%= page_title %>
<% end %>

<%= form_with model: @form, url: vaccine_batch_path(@vaccine, @batch), method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_date_field :expiry,
                         legend: { size: "s", text: "Expiry date" },
                         hint: { text: "For example, 27 10 2025" },
                         link_errors: true %>

  <%= f.govuk_submit "Save changes" %>
<% end %>
