<%= form_with model: resource, as: resource_name, url: user_session_path do |f| %>
  <%= f.govuk_error_summary %>

  <%= h1 "Log in", size: "xl" %>

  <%= f.govuk_email_field :email, autocomplete: "email", label: { text: "Email address" } %>
  <%= f.govuk_password_field :password, autocomplete: "current-password", label: { text: "Password" } %>

  <% if devise_mapping.rememberable? %>
    <%= f.govuk_check_boxes_fieldset :remember_me, multiple: false, legend: nil do %>
      <%= f.govuk_check_box :remember_me, 1, 0, multiple: false, link_errors: true, small: true, label: { text: "Remember me?" } %>
    <% end %>
  <% end %>

  <%= f.govuk_submit "Log in" %>
<% end %>
