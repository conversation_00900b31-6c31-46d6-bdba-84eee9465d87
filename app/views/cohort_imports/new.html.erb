<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(new_import_path, name: "import") %>
<% end %>

<% title = "Import child records" %>

<% content_for :page_title, title %>

<%= form_with model: @cohort_import, url: cohort_imports_path do |f| %>
  <%= f.govuk_error_summary %>

  <h1 class="nhsuk-heading-l"><%= title %></h1>

  <p>The file you upload should use the Mavis CSV format for child records.</p>

  <%= render AppImportFormatDetailsComponent.new(import: @cohort_import) %>

  <%= f.govuk_file_field :csv, label: { text: "Upload file", size: "m" } %>

  <%= f.govuk_submit %>
<% end %>
