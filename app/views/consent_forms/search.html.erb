<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(consent_form_path, name: "consent page") %>
<% end %>

<% page_title = "Search for a child record to match with #{@consent_form.full_name}" %>
<%= h1 page_title: do %>
  <span class="nhsuk-caption-l nhsuk-u-margin-top-2">
    Consent response from <%= @consent_form.parent_full_name %>
  </span>
  <%= page_title %>
<% end %>

<div class="nhsuk-grid-row">
  <div class="nhsuk-grid-column-one-third app-grid-column--sticky">
    <%= render AppCardComponent.new(colour: "blue") do |card| %>
      <% card.with_heading { "Consent response" } %>

      <%= govuk_summary_list(classes: %w[nhsuk-u-margin-bottom-4 nhsuk-summary-list--no-border app-summary-list--full-width]) do |summary_list|
            summary_list.with_row do |row|
              row.with_key { "Full name" }
              row.with_value { @consent_form.full_name }
            end
            summary_list.with_row do |row|
              row.with_key { "Date of birth" }
              row.with_value { patient_date_of_birth(@consent_form) }
            end
            summary_list.with_row do |row|
              row.with_key { "Postcode" }
              row.with_value { @consent_form.address_postcode }
            end
            summary_list.with_row do |row|
              row.with_key { "School" }
              row.with_value { patient_school(@consent_form) }
            end
          end %>

      <p class="nhsuk-body">
        <%= link_to "View full consent response", consent_form_path(@consent_form) %>
      </p>

      <%= govuk_button_link_to "Create new record", patient_consent_form_path(@consent_form), secondary: true %>
    <% end %>
  </div>

  <div class="nhsuk-grid-column-two-thirds">
    <%= render AppSearchComponent.new(form: @form, url: search_consent_form_path(@consent_form)) %>

    <%= render AppSearchResultsComponent.new(@pagy) do %>
      <% @patients.each do |patient| %>
        <%= render AppPatientSearchResultCardComponent.new(
              patient,
              link_to: match_consent_form_path(@consent_form, patient),
              show_parents: true,
              show_postcode: true,
              show_school: true,
            ) %>
      <% end %>
    <% end %>
  </div>
</div>
