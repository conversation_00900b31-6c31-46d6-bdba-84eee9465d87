<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= h1 "Home address" %>

  <p class="nhsuk-hint">
    Give the child’s primary address. We use this to confirm their identity.
  </p>

  <%= f.govuk_text_field :address_line_1,
                         label: { text: "Address line 1" },
                         autocomplete: "address-line1" %>

  <%= f.govuk_text_field :address_line_2,
                         label: { text: "Address line 2 (optional)" },
                         autocomplete: "address-line2" %>

  <%= f.govuk_text_field :address_town,
                         label: { text: "Town or city" },
                         autocomplete: "address-level2",
                         class: "nhsuk-u-width-two-thirds" %>

  <%= f.govuk_text_field :address_postcode,
                         label: { text: "Postcode" },
                         autocomplete: "postal-code",
                         class: "nhsuk-input--width-10" %>

  <%= f.govuk_submit "Continue" %>
<% end %>
