<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<%= h1 "Confirm your child’s school" %>

<%= govuk_inset_text classes: "nhsuk-u-margin-top-2 nhsuk-u-margin-bottom-4" do %>
  <p>
    <span class="nhsuk-heading-m nhsuk-u-margin-bottom-0" data-testid="school-name">
      <%= @consent_form.location.name %>
    </span>
    <%= format_address_single_line(@consent_form.location) %>
  </p>
<% end %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_radio_buttons_fieldset(:school_confirmed,
                                     legend: { size: "s", text: "Is this their school?" }) do %>
    <%= f.govuk_radio_button :school_confirmed, true,
                             link_errors: true,
                             label: { text: "Yes, they go to this school" } %>
    <%= f.govuk_radio_button :school_confirmed, false,
                             label: { text: "No, they go to a different school" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
