amends "package://github.com/jdx/hk/releases/download/v1.1.2/hk@1.1.2#/Config.pkl"
import "package://github.com/jdx/hk/releases/download/v1.1.2/hk@1.1.2#/Builtins.pkl"

local linters = new Mapping<String, Group> {
    ["all"] = new Group {
        steps = new Mapping<String, Step> {
            ["brakeman"] = new Step {
                check = "bin/brakeman --quiet --no-summary --no-pager"
            }

            ["prettier"] = new Step {
                batch = true
                glob = List("*.rb", "*.rake", "*.md", "*.js", "*.html", "*.erb", "*.css", "*.ejs", "*.mjs", "*.scss", "*.yaml", "*.json")
                check = "bin/prettier --check --ignore-unknown {{ files }}"
                check_list_files = "bin/prettier --list-different --ignore-unknown {{ files }}"
                fix = "bin/prettier --write --ignore-unknown {{ files }}"
            }

            ["rubocop"] = new Step {
                glob = List("*.rb", "*.rake")
                check = "bin/rubocop {{ files }}"
                fix = "bin/rubocop --autocorrect-all {{ files }}"
            }

            ["rufo"] = new Step {
                glob = "*.erb"
                exclude = List("**/layouts/*")
                check = "bin/rufo --check {{ files }}"
                fix = "bin/rufo --simple-exit {{ files }}"
            }

            ["terraform"] = Builtins.terraform

            ["tf_lint"] = new Step {
                glob = "*.tf"
                check = "tflint --chdir=terraform --config=$(pwd)/terraform/.tflint.hcl --recursive"
                fix = "tflint --chdir=terraform --config=$(pwd)/terraform/.tflint.hcl --recursive --fix"
            }
        }
    }

    local steps_with_staged_fixes = (linters["all"].steps) {
        for (key, value in linters["all"].steps) {
            [key] = (value) {
                stage = List("*")
            }
        }
    }

    ["pre-commit"] = new Group {
        steps = steps_with_staged_fixes.toMap().remove("brakeman").toMapping()
    }
}

hooks {
    ["pre-commit"] {
        fix = true
        stash = "patch-file"
        steps = new Mapping<String, Step | Group> {
            ["pre-commit"] = linters["pre-commit"]
            ["check_staged"] = new Step {
                check = "if git diff --staged --quiet; then echo 'No staged files aborting commit'; exit 1; fi"
                exclusive = true
            }
        }
    }

    ["fix"] {
        fix = true
        steps = linters["all"].steps
    }

    ["check"] {
        steps = linters["all"].steps
    }
}
