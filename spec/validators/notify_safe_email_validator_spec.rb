# frozen_string_literal: true

describe NotifySafeEmailValidator do
  subject(:model) { Validatable.new(email:) }

  before do
    stub_const("Validatable", Class.new).class_eval do
      include ActiveModel::Model
      attr_accessor :email
      validates :email, notify_safe_email: true
    end

    stub_const("ValidatableAllowNil", Class.new).class_eval do
      include ActiveModel::Model
      attr_accessor :email
      validates :email, notify_safe_email: { allow_nil: true }
    end

    stub_const("ValidatableAllowBlank", Class.new).class_eval do
      include ActiveModel::Model
      attr_accessor :email
      validates :email, notify_safe_email: { allow_blank: true }
    end
  end

  [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "firstname.o'<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    # "***********************************ögensberatung",
    "<EMAIL>",
    # "japanese-info@例え.テスト",
    "<EMAIL>"
  ].each do |value|
    context "with #{value}" do
      let(:email) { value }

      it { should be_valid }
    end
  end

  [
    nil,
    "",
    "email@***************",
    "email@[***************]",
    "plainaddress",
    "@no-local-part.com",
    "Outlook Contact <<EMAIL>>",
    "no-at.domain.com",
    "no-tld@domain",
    ";<EMAIL>",
    "<EMAIL>;uk",
    "<EMAIL>;",
    '"<EMAIL>',
    'email+middle"-<EMAIL>',
    '"quoted-local-part"@domain.com',
    '"<EMAIL>"',
    "<EMAIL>",
    "<EMAIL>",
    "multiple@<EMAIL>",
    "<NAME_EMAIL>",
    "spaces-in-domain@dom ain.com",
    "underscores-in-domain@dom_ain.com",
    "<EMAIL>|gov.uk",
    "comma,<EMAIL>",
    "comma-in-domain@domain,gov.uk",
    "pound-sign-in-local£@domain.com",
    "local-with-’-<EMAIL>",
    "local-with-”-<EMAIL>",
    "<EMAIL>",
    "brackets(in)<EMAIL>",
    "email-too-long-#{"a" * 320}@example.com",
    "<EMAIL>"
  ].each do |value|
    context "with #{value}" do
      let(:email) { value }

      it { should be_invalid }
    end
  end

  context "when allowing nil values" do
    subject(:model) { ValidatableAllowNil.new(email:) }

    let(:email) { nil }

    it { should be_valid }
  end

  context "when allowing blank values" do
    subject(:model) { ValidatableAllowBlank.new(email:) }

    let(:email) { "" }

    it { should be_valid }
  end
end
