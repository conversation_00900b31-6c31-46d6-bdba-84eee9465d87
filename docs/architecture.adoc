:imagesdir: images
:source-highlighter: pygments

ifdef::env-github[]
// If on GitHub, define attributes so we can find our diagram files and render
// them.

// The branch will be used to find the correct diagrams to render below.
// When PRing changes to the diagrams you can change this attributes
// temporarily to the name of the branch you're working on. But don't forget
// to change it back to main before merging!!
:github-branch: main

:github-repo: nhsuk/record-childrens-vaccinations

// URL for PlantUML Proxy. Using an attribute mainly because it's just tidier.
:plantuml-proxy-url: http://www.plantuml.com/plantuml/proxy?cache=no&src=

// Full path prefix we'll use for diagrams below.
:diagram-path-url: {plantuml-proxy-url}https://raw.githubusercontent.com/{github-repo}/{github-branch}/docs
endif::[]

:toc:

= Architecture

// Display toc of headings up to level 2
:sectnums:
:sectnumlevels: 2

== Introduction and Goals

This is the prototype a service to manage vaccinations in schools and during
catch-up clinics. This service will provide e-consent, programme management and
point-of-care services for SAIS organisations and parents. The goal is for this service
to integrate with the national vaccination record to record vaccinations.
However, integration with the national vaccination record will not be part of
this phase.

This service is in Alpha and this pilot is being used in to explore designs and
further our understandings of user requirements through user research.
Requirements have been tailored to focus on user-facing features over
non-functional requirements which have been, where possible, de-emphasised.

=== Requirements Overview

==== Functional Requirements

.Vaccination programme management
* Create and manage vaccination programmes
* Create and manage vaccines and batches
* Create and manage sessions
* Manage session cohorts

.Running sessions in schools
* Create requests to go to parents to give or refuse consent for their children
  to be vaccinated
* View and manage consent responses for each child
* Triage consent responses to ensure children are safe to vaccinate
* Record Gillick competence and consent responses from children
* Record vaccination outcomes for children during session

.Giving or receiving consent
* Parents can respond to requests to give or receive consent
* Child data is confirmed with a lookup to PDS

.Reporting vaccinations to NHSE
* Report the recorded vaccinations upstream to NHSE (DPS)
* Child information is validated with PDS
* Users are authenticated using CIS2
* Access to system functions are defined using national role-based access
  control (RBAC)

.Upload vaccination events to NHSE
* Users can upload vaccination records exported from their own systems

.User authentication
* Users are authenticated with the NHS Care Identity Service (CIS2)
* Users are part of a SAIS organisation and have restricted access to only those
  programme and session data owned by their organisation
* User SAIS organisation information is retrieved from NHS CIS2
* Access to functionality will be tied to user's roles as defined in the
    national RBAC, fox example only nurses are authorised to record a
    vaccination
* No authentication is required for parents to give or refuse consent

==== Non-Functional Requirements

* All user authentication will be done using NHS CIS2
* The service will be available on the public internet with no IP address
  range restrictions

<<<<
== Architecture Constraints

* The system must protect patient data and comply with all applicable laws and
  regulations.
* The system will adhere to the applicable NHS architecture and design
  principles.
* The system must be deployed to a cloud platform, but also be platform agnostic
  and not constrained to run on any one cloud platform.
* The system must be deployed to NHS approved platforms.
* Integration with NHS DPS is via MESH using background batched jobs.
* Integration with NHS PDS is via FHIR API using background jobs.

<<<<
== System Scope and Context

ifdef::env-github[]
image::{diagram-path-url}/diagrams/context-view.puml[Context view diagram]
endif::[]

ifndef::env-github[]
[plantuml,align="center"]
----
include::diagrams/context-view.puml[]
----
endif::[]

SAIS Organisation::
Organisation responsible for performing vaccinations on school-aged children.

Parents::
Parents are notified of the planned vaccination programme and invited to give
consent for their children to be vaccinated.

NHS Personal demographic service (PDS)::
Used to lookup NHS numbers and confirm information entered / uploaded.

NHS Data Processing Service (DPS)::
Upstream recipient of vaccination records.


== Solution strategy

Certain solution strategy decisions are recorded as architecture decisions in
the Architecture Decision Records (ADRs), which can be found in link:../adr/[the
`adr` directory at the root of this repository]. Relevant ADRs:

* link:../adr/00002-begin-with-a-monolithic-application-architecture.md[ADR 2:
  Application Architecture]
* link:../adr/00004-language-and-framework.md[ADR 4: Language and framework]

== Building Blocks View

=== Level 1: Container View

ifdef::env-github[]
image::{diagram-path-url}/diagrams/container-view.puml[Container view diagram]
endif::[]

ifndef::env-github[]
[plantuml,align="center"]
----
include::diagrams/container-view.puml[]
----
endif::[]

GOVUK Notify::
This service used to send consent confirmations and other notifications to
parents. It is an external service run by Government Digital Service (GDS) and
in use by other NHS services.

=== Level 2: Component View

ifdef::env-github[]
image::{diagram-path-url}/diagrams/component-view.puml[Component view diagram]
endif::[]

ifndef::env-github[]
[plantuml,align="center"]
----
include::diagrams/component-view.puml[]
----
endif::[]

Database::
Relational storage used to store application data including programme data,
consent responses and vaccination events. In the case of the vaccination events,
this data would be uploaded to the national vaccination record and this data
store would be used as temporary storage. However during the alpha phase
uploading to the national vaccination record may not available, so the
vaccination event data may be kept in this store until the end of the pilot.

Sending email and SMS::
For the current scale of this pilot the sending of emails will be done directly
from the web servers. It is common for services to use separate workers to
process API integration with external services to isolate the web application
from possible network issues, but won't be necessary with the number of users
using the pilot.

== Runtime View

The Manage vaccinations in schools service is built largely as a
standard server-rendered web application: HTML pages are rendered on the server
and delivered along with CSS and JavaScript to the client. Users login to the
service using a standard login page, and as is standard with Ruby on Rails apps,
resources are exposed with REST-like paths using an MVC approach to separate
concerns on the server.

== Deployment

include::deployment.adoc[leveloffset=+1,lines=24..-1]

== Components

* Authentication
* Programme management - creation, update, etc
* Programme operations and vaccination recording
* Offline support - Browser-based component
* FHIR server synchronisation
