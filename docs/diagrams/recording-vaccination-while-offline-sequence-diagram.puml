@startuml

participant "User" as user
participant "Front-End" as frontend
participant "Service Worker" as sw
participant "Server App" as server

== Client is offline ==
sw ->x server: ping
note left: Service worker checks if it can access the server
note right: No response
sw -> sw: set status to offline

user -> frontend: View programme
frontend -> sw: get programme pages
frontend <-- sw: programme page

user -> frontend: Record vaccination
frontend -> sw: post vaccination record
sw -> sw: save vaccination record
frontend <-- sw: ok

== Client comes back online ==
sw -> server: ping
sw <-- server: ok
sw -> sw: set status to online
sw -> server: post vaccination record
sw <-- server: ok

@enduml
