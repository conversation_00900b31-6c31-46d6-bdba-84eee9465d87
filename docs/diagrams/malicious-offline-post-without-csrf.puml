@startuml

title Malicious offline POST without CSRF

autoactivate on

== User is still authenticated with manage.nhs.uk ==

group Malicious update to patient data
    Browser -> evil.site: GET /
    return 200 OK
    note right
        // Malicious form that POSTs to manage.nhs.uk
        <form action="https://manage.nhs.uk/patients/1/vaccinations" ...>
          <input type="hidden" name="vaccination" value="mmr" />
          <input type="hidden" name="vaccination_given" value="false" />
        ...
    end note

    Browser -> manage.nhs.uk: POST /patients/1/vaccinations
    note right
        vaccine=mmr
        vaccination_performed=false
        // No CSRF token needed
    end note
    return 200 OK
end

@enduml
