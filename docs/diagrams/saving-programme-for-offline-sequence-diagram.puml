@startuml

participant "User" as user
participant "Front-End" as frontend
participant "Service Worker" as sw
participant "Server App" as server

user -> frontend: Click "Save programme for offline use"\n on programme page
frontend -> sw: message("Save programme 1 for offline")
note over sw, server: The service worker requests resources it will need for offline use.
sw -> server: get programme pages and data
sw <-- server: requested resources

@enduml
