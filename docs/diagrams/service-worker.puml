@startuml

title Manage vaccinations in schools service worker

!include <C4/C4_Component.puml>

LAYOUT_TOP_DOWN()

System_Boundary(manage, "Manage vaccinations in schools") {
  Container_Boundary(browser, "Web Browser") {
    Component(frontend, "Front-End", "HTML, Javascript")
    Component(serviceWorker, "Service Worker", "Javascript")
    ComponentDb(indexedDB, "Cache", "IndexedDB")
    ComponentDb(cacheDB, "Cache", "Browser Cache")
  }

  Container(server, "Server App", "Ruby on Rails")
}

Lay_D(cacheDB, indexedDB)
Rel(frontend, serviceWorker, "Get Pages and Data")
Rel(serviceWorker, indexedDB, "Cache Data")
Rel_L(serviceWorker, cacheDB, "Cache Pages")
Rel(serviceWorker, server, "Get Pages and Data")

@enduml
