@startuml

title Successful offline POST without CSRF

autoactivate on

group Prepare to work offline
    Browser -> manage.nhs.uk: GET /patients/1/vaccinations/new
    return 200 OK
    note right
        // HTML form without CSRF token
    end note
end

== Browser goes offline ==

group Perform offline work
    Browser --> Browser: Records vaccination
end

== Browser comes back online ==

group Update patient data
    Browser -> manage.nhs.uk: POST /patients/1/vaccinations/
    note right
        vaccination=mmr
        vaccination_performed=false
    end note
    return 200 OK
end

@enduml
