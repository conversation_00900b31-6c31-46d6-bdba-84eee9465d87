inherit_gem:
  rubocop-govuk:
    - config/default.yml
    - config/rails.yml
    - config/rspec.yml

inherit_from:
  - node_modules/@prettier/plugin-ruby/rubocop.yml

inherit_mode:
  merge:
    - Exclude

AllCops:
  Exclude:
    - scratchpad/*

Layout/EmptyLineAfterMagicComment:
  Enabled: true

Layout/LineLength:
  AllowedPatterns: [idx_on]
  Exclude:
    - db/schema.rb

Lint/PercentStringArray:
  Exclude:
    - spec/models/dps_export_spec.rb

Rails/CreateTableWithTimestamps:
  Exclude:
    - db/schema.rb

Rails/LexicallyScopedActionFilter:
  Exclude:
    - app/controllers/users/sessions_controller.rb

Rails/Output:
  Exclude:
    - app/lib/mavis_cli/**/*.rb

Rails/UnknownEnv:
  Environments:
    - development
    - production
    - staging
    - test

RSpec/BeforeAfterAll:
  Exclude:
    - spec/jobs/email_delivery_job_spec.rb
    - spec/jobs/sms_delivery_job_spec.rb

RSpec/ContextWording:
  Enabled: false

RSpec/ImplicitExpect:
  EnforcedStyle: should

RSpec/VerifiedDoubles:
  Exclude:
    - spec/controllers/concerns/consent_form_mailer_concern_spec.rb
    - spec/controllers/concerns/triage_mailer_concern_spec.rb
    - spec/controllers/concerns/vaccination_mailer_concern_spec.rb

Style/FrozenStringLiteralComment:
  Enabled: true
  EnforcedStyle: always_true
  Exclude:
    - db/schema.rb

Style/NumericLiterals:
  Exclude:
    - db/schema.rb

Rails/ApplicationController:
  Exclude:
    - app/controllers/errors_controller.rb
