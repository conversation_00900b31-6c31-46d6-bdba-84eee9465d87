default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS", 5).to_i + ENV.fetch("GOOD_JOB_MAX_THREADS", 4).to_i %>

development:
  <<: *default
  database: manage_vaccinations_development
test:
  <<: *default
  database: manage_vaccinations_test
staging:
  <<: *default
  database: manage_vaccinations_staging
production:
  <<: *default
  database: manage_vaccinations_production
