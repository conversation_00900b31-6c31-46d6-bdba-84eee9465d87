{"ignored_warnings": [{"warning_type": "File Access", "warning_code": 16, "fingerprint": "2d210ea9765576c7e2daeeab6615fd785dc531224c2bb3ffab15d8d64a45fd14", "check_name": "SendFile", "message": "Parameter value used in file name", "file": "app/controllers/programmes_controller.rb", "line": 62, "link": "https://brakemanscanner.org/docs/warning_types/file_access/", "code": "send_file(\"public/consent_forms/#{authorize(policy_scope(Programme).find_by!(:type => params[:type])).type}.pdf\", :filename => (\"#{authorize(policy_scope(Programme).find_by!(:type => params[:type])).name} Consent Form.pdf\"), :disposition => \"attachment\")", "render_path": null, "location": {"type": "method", "class": "ProgrammesController", "method": "consent_form"}, "user_input": "params[:type]", "confidence": "Weak", "cwe_id": [22], "note": "Programme names/types come from a limited set of enums."}], "brakeman_version": "7.0.0"}