name: Continuous deployment
run-name: Continuous deployment of ${{ github.ref_name }} to QA and Test

on:
  push:
    branches: [next]

jobs:
  test:
    permissions: {}
    uses: ./.github/workflows/test.yml
  deploy:
    needs: test
    strategy:
      fail-fast: false
      matrix:
        environment: [qa, test]
    permissions:
      id-token: write
    uses: ./.github/workflows/deploy.yml
    with:
      environment: ${{ matrix.environment }}
      server_types: all
