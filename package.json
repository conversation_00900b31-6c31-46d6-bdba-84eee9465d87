{"name": "app", "private": "true", "dependencies": {"@hotwired/stimulus": "^3.2.2", "@hotwired/turbo-rails": "^8.0.16", "accessible-autocomplete": "^3.0.1", "esbuild": "^0.25.5", "govuk-frontend": "^5.10.2", "idb": "^8.0.3", "nhsuk-frontend": "9.6.1", "sass": "^1.89.2", "stimulus": "^3.2.2", "workbox-build": "^7.3.0"}, "scripts": {"build:css": "sass ./app/assets/stylesheets/application.scss:./app/assets/builds/application.css --no-source-map --load-path=node_modules --quiet-deps --style compressed", "build": "esbuild app/javascript/*.[jt]s app/javascript/controllers/*.[jt]s --bundle --sourcemap --outdir=app/assets/builds --public-path=assets --minify", "build:serviceworker": "esbuild app/javascript/serviceworker/main.js --bundle --sourcemap --outfile=public/sw.js --minify", "test:e2e": "PW_EXPERIMENTAL_SERVICE_WORKER_NETWORK_EVENTS=1 playwright test", "test:load": "artillery run tests/load.yml", "test": "jest"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@playwright/test": "^1.53.0", "@prettier/plugin-ruby": "^4.0.4", "@types/jest": "^30.0.0", "esbuild-jest": "^0.5.0", "fake-indexeddb": "^4.0.2", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "jest-fetch-mock": "^3.0.3", "mutationobserver-shim": "^0.3.7", "officecrypto-tool": "^0.0.18", "playwright-core": "^1.53.0", "prettier": "^3.5.3"}, "jest": {"collectCoverage": true, "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "testPathIgnorePatterns": ["app/assets/builds/", "node_modules/", "tests"], "transformIgnorePatterns": [], "testEnvironment": "jsdom", "transform": {"^.+\\.(j|t)sx?$": ["esbuild-jest", {"sourcemap": true}]}}}